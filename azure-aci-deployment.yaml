apiVersion: 2021-07-01
location: eastus
name: lightrag-deployment
properties:
  containers:
  - name: lightrag-app
    properties:
      image: hkuds/lightrag:latest
      resources:
        requests:
          cpu: 2
          memoryInGb: 8
      ports:
      - port: 8020
        protocol: TCP
      environmentVariables:
      - name: OPENAI_API_KEY
        secureValue: "your-openai-api-key"
      - name: POSTGRES_HOST
        value: "your-postgres-server.postgres.database.azure.com"
      - name: POSTGRES_USER
        value: "lightrag_user"
      - name: POSTGRES_PASSWORD
        secureValue: "your-postgres-password"
      - name: POSTGRES_DB
        value: "lightrag_db"
  - name: postgres-rag
    properties:
      image: shangor/postgres-for-rag:latest
      resources:
        requests:
          cpu: 1
          memoryInGb: 4
      ports:
      - port: 5432
        protocol: TCP
      environmentVariables:
      - name: POSTGRES_DB
        value: "lightrag_db"
      - name: POSTGRES_USER
        value: "lightrag_user"
      - name: POSTGRES_PASSWORD
        secureValue: "your-postgres-password"
      volumeMounts:
      - name: postgres-storage
        mountPath: /var/lib/postgresql/data
  volumes:
  - name: postgres-storage
    azureFile:
      shareName: postgres-data
      storageAccountName: yourstorageaccount
      storageAccountKey: your-storage-key
  osType: Linux
  restartPolicy: Always
  ipAddress:
    type: Public
    ports:
    - protocol: TCP
      port: 8020
    - protocol: TCP
      port: 5432
type: Microsoft.ContainerInstance/containerGroups
